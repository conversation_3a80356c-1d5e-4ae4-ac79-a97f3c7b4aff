# DevLake Environment Configuration
# Copy this to .env.local and update with your actual values

# Timezone
TZ=Asia/Karachi

# MySQL Configuration
MYSQL_ROOT_PASSWORD=devlake_root_password_2024
MYSQL_DATABASE=lake
MYSQL_USER=merico
MYSQL_PASSWORD=merico_password_2024

# DevLake Database URL
DB_URL=mysql://merico:merico_password_2024@mysql:3306/lake?charset=utf8mb4&parseTime=True&loc=Local

# DevLake Encryption Secret (IMPORTANT: Change this to a random 32-character string)
ENCRYPTION_SECRET=f1f91e46ba0ef53dbd5874ddcd733d17

# Grafana Configuration
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin123

# Optional: Additional Grafana plugins
GRAFANA_PLUGINS=

# Logging Configuration
LOGGING_LEVEL=INFO
LOGGING_DIR=logs

# GitLab Configuration (to be set via Config UI)
# GITLAB_URL=https://gitlab.com/api/v4
# GITLAB_TOKEN=your_gitlab_personal_access_token

# Jira Configuration (to be set via Config UI)
# JIRA_URL=https://your-domain.atlassian.net/rest/
# JIRA_EMAIL=<EMAIL>
# JIRA_API_TOKEN=your_jira_api_token

# AWS Configuration (for future deployment)
# AWS_REGION=us-east-1
# AWS_RDS_ENDPOINT=your-rds-endpoint
# AWS_SECRETS_MANAGER_SECRET_NAME=devlake-secrets
