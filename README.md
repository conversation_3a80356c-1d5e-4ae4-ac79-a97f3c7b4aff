# Apache DevLake - Coding Time Metrics Dashboard

This repository contains a complete setup for Apache DevLake with GitLab and Jira Cloud integration, specifically configured to track **Coding Time** metrics - the time from first commit to when a PR/MR is ready for review.

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- GitLab Personal Access Token with `read_api` scope
- Jira Cloud API token and email

### 1. Initial Setup

```bash
# Clone and setup
git clone <your-repo>
cd devlake-setup

# Run the setup script
./scripts/setup.sh
```

### 2. Configure Integrations

1. **Open Config UI**: http://localhost:4000
2. **Add GitLab Connection**:
   - Endpoint: `https://gitlab.com/api/v4`
   - Token: Your GitLab PAT
3. **Add Jira Connection**:
   - Endpoint: `https://yourcompany.atlassian.net/rest/`
   - Email: Your Jira email
   - Token: Your Jira API token

### 3. View Dashboards

- **Grafana**: http://localhost:3002 (admin/admin123)
- **Config UI**: http://localhost:4000
- **API**: http://localhost:8080

## 📊 Coding Time Metric

The **Coding Time** metric measures the time from:
- **Start**: First commit in a merge request
- **End**: When MR is marked "Ready for Review" (draft → ready)

This helps track actual development time before code review begins.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Config UI     │    │   DevLake API   │    │    Grafana      │
│   Port: 4000    │    │   Port: 8080    │    │   Port: 3002    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL DB      │
                    │   Port: 3306    │
                    └─────────────────┘
```

## 📁 Project Structure

```
├── docker-compose.yml          # Main Docker Compose configuration
├── .env                        # Environment template
├── scripts/
│   ├── setup.sh               # Initial setup script
│   └── aws-deploy.sh          # AWS deployment script
├── config/
│   └── config.yaml            # DevLake configuration
├── mysql/
│   └── init.sql               # MySQL initialization
├── grafana/
│   ├── provisioning/          # Grafana auto-configuration
│   └── dashboards/            # Custom dashboards
└── docs/
    ├── gitlab-integration.md   # GitLab setup guide
    ├── jira-integration.md     # Jira setup guide
    └── aws-deployment.md       # AWS deployment guide
```

## 🔧 Configuration

### Environment Variables

Copy `.env` to `.env.local` and update:

```bash
# Required: Update these values
ENCRYPTION_SECRET=your-32-character-encryption-secret
GITLAB_TOKEN=your_gitlab_personal_access_token
JIRA_API_TOKEN=your_jira_api_token
JIRA_EMAIL=<EMAIL>

# Optional: Customize as needed
TZ=Asia/Karachi
GRAFANA_ADMIN_PASSWORD=admin123
```

### GitLab Integration

1. Create Personal Access Token with `read_api` scope
2. Add connection in Config UI
3. Select repositories to monitor
4. Configure sync frequency (recommended: 6 hours)

See [GitLab Integration Guide](docs/gitlab-integration.md) for details.

### Jira Integration

1. Create API token at https://id.atlassian.com/manage-profile/security/api-tokens
2. Add connection in Config UI
3. Select projects/boards to monitor
4. Configure sync frequency (recommended: 4 hours)

See [Jira Integration Guide](docs/jira-integration.md) for details.

## 📈 Custom Dashboards

### Coding Time Dashboard

The included dashboard shows:
- Average coding time by repository
- Coding time trends over time
- Top 10 longest coding time PRs
- Distribution of coding times

### Custom SQL Queries

```sql
-- Average coding time by repository (last 30 days)
SELECT 
  repos.name as repository,
  ROUND(AVG(ppm.pr_coding_time / 60), 2) AS avg_coding_time_hours
FROM project_pr_metrics ppm
JOIN pull_requests pr ON pr.id = ppm.id
JOIN repos ON repos.id = pr.base_repo_id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY repos.name
ORDER BY avg_coding_time_hours DESC;
```

## ☁️ AWS Deployment

This setup is designed to be easily portable to AWS:

### Deployment Options

1. **ECS Fargate** (Recommended)
   - Serverless containers
   - Auto-scaling
   - Managed infrastructure

2. **EC2 with Docker Compose**
   - Cost-effective
   - Full control
   - Manual scaling

3. **EKS (Kubernetes)**
   - Advanced orchestration
   - Multi-service deployments

### Quick AWS Setup

```bash
# Prepare for AWS deployment
./scripts/aws-deploy.sh --region us-east-1 --environment production

# Follow the AWS deployment guide
cat docs/aws-deployment.md
```

See [AWS Deployment Guide](docs/aws-deployment.md) for detailed instructions.

## 🔍 Monitoring and Troubleshooting

### Health Checks

```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs -f devlake
docker-compose logs -f grafana

# Test API endpoints
curl http://localhost:8080/api/ping
curl http://localhost:4000
curl http://localhost:3002
```

### Common Issues

1. **Services not starting**: Check Docker resources and port conflicts
2. **Database connection errors**: Verify MySQL credentials in `.env.local`
3. **GitLab/Jira sync issues**: Check API tokens and permissions
4. **Missing data**: Ensure projects are selected and sync jobs are running

## 📚 Documentation

- [GitLab Integration](docs/gitlab-integration.md)
- [Jira Integration](docs/jira-integration.md)
- [AWS Deployment](docs/aws-deployment.md)
- [DevLake Official Docs](https://devlake.apache.org/docs/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test locally
5. Submit a pull request

## 📄 License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- [DevLake Community](https://github.com/apache/incubator-devlake)
- [Issues](https://github.com/your-org/devlake-setup/issues)
- [Discussions](https://github.com/your-org/devlake-setup/discussions)
