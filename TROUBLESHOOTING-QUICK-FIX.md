# Quick Fix for DevLake Health Check Issue

## Problem
You encountered the error: `dependency failed to start: container devlake-api is unhealthy`

## Root Cause
The health check in the Docker Compose configuration was using the wrong endpoint:
- **Wrong**: `/api/ping` (returns 404)
- **Correct**: `/ping` (returns 200)

## Solution Applied
1. **Fixed the health check endpoint** in `docker-compose.yml`:
   ```yaml
   healthcheck:
     test: ["CMD", "curl", "-f", "http://localhost:8080/ping"]  # Fixed from /api/ping
   ```

2. **Removed obsolete version field** from `docker-compose.yml` to eliminate warnings

3. **Updated validation script** to use correct endpoint

## Verification
After applying the fix, all services should be healthy:

```bash
# Check service status
docker-compose ps

# Expected output:
# NAME                STATUS
# devlake-api         Up X minutes (healthy)
# devlake-config-ui   Up X minutes
# devlake-grafana     Up X minutes
# devlake-mysql       Up X minutes (healthy)
```

## Test Endpoints
```bash
# DevLake API
curl http://localhost:8080/ping
# Should return: 200 OK

# Config UI
curl -I http://localhost:4000
# Should return: 200 OK

# Grafana
curl -I http://localhost:3002
# Should return: 302 Found (redirect to login)
```

## If You Still Have Issues

1. **Restart services**:
   ```bash
   docker-compose down
   docker-compose --env-file .env.local up -d
   ```

2. **Check logs**:
   ```bash
   docker-compose logs devlake
   docker-compose logs config-ui
   ```

3. **Run validation**:
   ```bash
   ./scripts/validate-setup.sh
   ```

## Next Steps
Once all services are healthy:
1. Open Config UI: http://localhost:4000
2. Add GitLab connection with your Personal Access Token
3. Add Jira connection with your API token
4. View dashboards: http://localhost:3002 (admin/admin123)

## Files Modified
- `docker-compose.yml` - Fixed health check endpoint
- `scripts/validate-setup.sh` - Updated endpoint validation

The setup is now working correctly and ready for GitLab/Jira integration!
