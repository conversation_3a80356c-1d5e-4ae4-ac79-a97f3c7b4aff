# DevLake Configuration File
# This file contains default configuration for DevLake

# API Configuration
api:
  port: 8080
  timeout: 120s

# Database Configuration
database:
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600s

# Plugin Configuration
plugins:
  gitlab:
    enabled: true
    rate_limit: 2000  # requests per hour
    proxy_url: ""
  
  jira:
    enabled: true
    rate_limit: 1000  # requests per hour
    proxy_url: ""

# Logging Configuration
logging:
  level: INFO
  format: json
  output: stdout

# Metrics Configuration
metrics:
  enabled: true
  port: 9090

# Security Configuration
security:
  cors:
    enabled: true
    allowed_origins: ["http://localhost:4000", "http://localhost:3002"]
