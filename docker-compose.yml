version: "3.8"

services:
  # MySQL Database for DevLake
  mysql:
    image: mysql:8.0
    container_name: devlake-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      TZ: ${TZ}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_bin --skip-log-bin
    networks:
      - devlake-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5

  # DevLake Core API
  devlake:
    image: apache/devlake:v0.20.0-beta1
    container_name: devlake-api
    restart: unless-stopped
    environment:
      DB_URL: ${DB_URL}
      ENCRYPTION_SECRET: ${ENCRYPTION_SECRET}
      TZ: ${TZ}
      LOGGING_LEVEL: ${LOGGING_LEVEL:-INFO}
      LOGGING_DIR: ${LOGGING_DIR:-logs}
    ports:
      - "8080:8080"
    volumes:
      - devlake_logs:/app/logs
      - ./config:/app/config:ro
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - devlake-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # DevLake Config UI
  config-ui:
    image: apache/devlake-config-ui:v0.20.0-beta1
    container_name: devlake-config-ui
    restart: unless-stopped
    environment:
      DEVLAKE_ENDPOINT: http://devlake:8080
      GRAFANA_ENDPOINT: http://grafana:3000
      TZ: ${TZ}
    ports:
      - "4000:4000"
    depends_on:
      devlake:
        condition: service_healthy
    networks:
      - devlake-network

  # Grafana Dashboard
  grafana:
    image: apache/devlake-dashboard:v0.20.0-beta1
    container_name: devlake-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_ADMIN_USER}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: "false"
      GF_INSTALL_PLUGINS: ${GRAFANA_PLUGINS:-}
      TZ: ${TZ}
    ports:
      - "3002:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - devlake-network

volumes:
  mysql_data:
    driver: local
  grafana_data:
    driver: local
  devlake_logs:
    driver: local

networks:
  devlake-network:
    driver: bridge
