# AWS Deployment Guide

This guide explains how to deploy DevLake to AWS using various services.

## Architecture Options

### Option 1: ECS Fargate (Recommended)
- **Pros**: Serverless, auto-scaling, managed
- **Cons**: Higher cost for persistent workloads
- **Best for**: Variable workloads, minimal ops overhead

### Option 2: EC2 with Docker Compose
- **Pros**: Cost-effective, full control
- **Cons**: Manual scaling, more maintenance
- **Best for**: Predictable workloads, cost optimization

### Option 3: EKS (Kubernetes)
- **Pros**: Advanced orchestration, multi-service deployments
- **Cons**: Complex setup, higher learning curve
- **Best for**: Large-scale deployments, existing K8s expertise

## Prerequisites

1. AWS CLI configured with appropriate permissions
2. Docker images pushed to ECR (optional)
3. RDS MySQL instance
4. VPC with proper security groups

## Option 1: ECS Fargate Deployment

### 1. Create RDS MySQL Instance

```bash
aws rds create-db-instance \
  --db-instance-identifier devlake-mysql \
  --db-instance-class db.t3.micro \
  --engine mysql \
  --engine-version 8.0.35 \
  --master-username admin \
  --master-user-password YourSecurePassword123 \
  --allocated-storage 20 \
  --vpc-security-group-ids sg-xxxxxxxxx \
  --db-subnet-group-name your-db-subnet-group \
  --backup-retention-period 7 \
  --storage-encrypted
```

### 2. Store Secrets in AWS Secrets Manager

```bash
aws secretsmanager create-secret \
  --name devlake-secrets \
  --description "DevLake configuration secrets" \
  --secret-string '{
    "MYSQL_ROOT_PASSWORD": "YourSecurePassword123",
    "MYSQL_PASSWORD": "merico_password_2024",
    "ENCRYPTION_SECRET": "your-32-character-encryption-secret",
    "GRAFANA_ADMIN_PASSWORD": "admin123",
    "GITLAB_TOKEN": "your_gitlab_token",
    "JIRA_API_TOKEN": "your_jira_token"
  }'
```

### 3. Create ECS Task Definition

Create `aws/ecs-task-definition.json`:

```json
{
  "family": "devlake",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "devlake",
      "image": "apache/devlake:v0.20.0-beta1",
      "portMappings": [{"containerPort": 8080}],
      "environment": [
        {"name": "TZ", "value": "Asia/Karachi"}
      ],
      "secrets": [
        {"name": "DB_URL", "valueFrom": "arn:aws:secretsmanager:region:account:secret:devlake-secrets:DB_URL::"},
        {"name": "ENCRYPTION_SECRET", "valueFrom": "arn:aws:secretsmanager:region:account:secret:devlake-secrets:ENCRYPTION_SECRET::"}
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/devlake",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### 4. Create Application Load Balancer

```bash
aws elbv2 create-load-balancer \
  --name devlake-alb \
  --subnets subnet-xxxxxxxx subnet-yyyyyyyy \
  --security-groups sg-xxxxxxxxx
```

### 5. Configure Target Groups and Listeners

Create target groups for each service and configure ALB listeners to route traffic:
- `/` → Config UI (port 4000)
- `/grafana` → Grafana (port 3000)
- `/api` → DevLake API (port 8080)

## Option 2: EC2 Deployment

### 1. Launch EC2 Instance

```bash
aws ec2 run-instances \
  --image-id ami-0abcdef1234567890 \
  --count 1 \
  --instance-type t3.medium \
  --key-name your-key-pair \
  --security-group-ids sg-xxxxxxxxx \
  --subnet-id subnet-xxxxxxxx \
  --user-data file://scripts/ec2-user-data.sh
```

### 2. EC2 User Data Script

Create `scripts/ec2-user-data.sh`:

```bash
#!/bin/bash
yum update -y
yum install -y docker
systemctl start docker
systemctl enable docker
usermod -a -G docker ec2-user

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Clone your DevLake configuration
cd /home/<USER>
git clone https://github.com/your-org/devlake-config.git
cd devlake-config

# Start DevLake
docker-compose up -d
```

## Environment Variables for AWS

Update your `.env` file for AWS deployment:

```bash
# AWS-specific configuration
DB_URL=mysql://admin:<EMAIL>:3306/lake?charset=utf8mb4&parseTime=True&loc=Local

# Use AWS Secrets Manager for sensitive data
# These will be injected by ECS or retrieved by your application
```

## Security Considerations

1. **VPC Configuration**: Deploy in private subnets with NAT Gateway
2. **Security Groups**: Restrict access to necessary ports only
3. **IAM Roles**: Use least-privilege access for ECS tasks
4. **Secrets Management**: Never hardcode secrets in images
5. **SSL/TLS**: Use ACM certificates with ALB
6. **Backup**: Enable automated RDS backups

## Monitoring and Logging

1. **CloudWatch Logs**: Configure log groups for each service
2. **CloudWatch Metrics**: Monitor CPU, memory, and custom metrics
3. **AWS X-Ray**: Enable distributed tracing (optional)
4. **Health Checks**: Configure ALB health checks

## Cost Optimization

1. **Right-sizing**: Start with smaller instances and scale up
2. **Reserved Instances**: Use RIs for predictable workloads
3. **Spot Instances**: Consider for non-critical environments
4. **Auto Scaling**: Configure based on metrics
5. **Storage**: Use GP3 volumes for better price/performance
