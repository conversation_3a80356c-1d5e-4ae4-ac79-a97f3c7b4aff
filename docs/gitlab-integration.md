# GitLab Integration Guide

## Prerequisites

1. GitLab account with access to repositories you want to monitor
2. Personal Access Token with appropriate permissions

## Creating GitLab Personal Access Token

1. Go to GitLab → User Settings → Access Tokens
2. Create a new token with the following scopes:
   - `read_api` - Required for accessing GitLab API
   - `read_repository` - Required for accessing repository data
   - `read_user` - Required for user information

## Configuration Steps

### 1. Access DevLake Config UI

Open http://localhost:4000 in your browser.

### 2. Add GitLab Connection

1. Click on "Connections" in the sidebar
2. Click "Add Connection"
3. Select "GitLab"
4. Fill in the connection details:
   - **Name**: Give your connection a name (e.g., "GitLab Cloud")
   - **Endpoint**: `https://gitlab.com/api/v4`
   - **Personal Access Token**: Your GitLab PAT

### 3. Test Connection

Click "Test Connection" to verify your credentials work correctly.

### 4. Select Projects

1. After saving the connection, go to "Projects"
2. Select the GitLab projects you want to monitor
3. Configure sync settings:
   - **Sync Frequency**: How often to sync data (recommended: every 6 hours)
   - **Data Range**: How far back to sync historical data

## Data Collected

DevLake will collect the following GitLab data:

- **Merge Requests (MRs)**
  - Creation, update, and merge times
  - Author and reviewer information
  - Status changes and approvals
  - Comments and discussions

- **Commits**
  - Commit messages and metadata
  - Author and committer information
  - File changes and statistics

- **Pipelines** (optional)
  - Pipeline status and duration
  - Job information and logs

## Coding Time Calculation

The Coding Time metric is calculated as:
- **Start**: Time of the first commit in the MR
- **End**: Time when the MR is marked as "Ready for Review" (draft → ready)

This metric helps track how long developers spend actively coding before requesting review.

## Troubleshooting

### Common Issues

1. **403 Forbidden**: Check your PAT permissions
2. **Rate Limiting**: Reduce sync frequency or contact GitLab support
3. **Missing Data**: Ensure projects are properly selected and synced

### Logs

Check DevLake logs for detailed error information:
```bash
docker-compose logs devlake
```
