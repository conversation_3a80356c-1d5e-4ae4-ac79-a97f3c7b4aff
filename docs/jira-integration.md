# Jira Cloud Integration Guide

## Prerequisites

1. Jira Cloud instance (e.g., yourcompany.atlassian.net)
2. Jira account with appropriate permissions
3. API token for authentication

## Creating Jira API Token

1. Go to https://id.atlassian.com/manage-profile/security/api-tokens
2. Click "Create API token"
3. Give it a descriptive name (e.g., "DevLake Integration")
4. Copy the generated token (you won't see it again)

## Configuration Steps

### 1. Access DevLake Config UI

Open http://localhost:4000 in your browser.

### 2. Add Jira Connection

1. Click on "Connections" in the sidebar
2. Click "Add Connection"
3. Select "Jira"
4. Fill in the connection details:
   - **Name**: Give your connection a name (e.g., "Jira Cloud")
   - **Endpoint**: `https://yourcompany.atlassian.net/rest/`
   - **Email**: Your Jira account email
   - **API Token**: The token you created above

### 3. Test Connection

Click "Test Connection" to verify your credentials work correctly.

### 4. Select Projects/Boards

1. After saving the connection, go to "Projects"
2. Select the Jira projects or boards you want to monitor
3. Configure sync settings:
   - **Sync Frequency**: How often to sync data (recommended: every 4 hours)
   - **Issue Types**: Select which issue types to sync (Story, Bug, Task, etc.)
   - **Historical Data**: How far back to sync (recommended: 6 months)

## Data Collected

DevLake will collect the following Jira data:

- **Issues**
  - Issue details (summary, description, type, priority)
  - Status transitions and workflow states
  - Assignee and reporter information
  - Time tracking data
  - Custom fields

- **Sprints** (if using Scrum)
  - Sprint information and dates
  - Sprint goals and capacity
  - Issue assignments to sprints

- **Epics**
  - Epic details and relationships
  - Child issue associations

## Linking with GitLab

To link Jira issues with GitLab merge requests:

1. **Include Jira issue keys in commit messages**:
   ```
   git commit -m "PROJ-123: Fix authentication bug"
   ```

2. **Include Jira issue keys in branch names**:
   ```
   git checkout -b feature/PROJ-123-authentication-fix
   ```

3. **Include Jira issue keys in MR titles**:
   ```
   PROJ-123: Implement user authentication
   ```

## Metrics and Insights

With Jira integration, you can track:

- **Lead Time**: Time from issue creation to resolution
- **Cycle Time**: Time from work start to completion
- **Story Points**: Velocity and capacity planning
- **Issue Flow**: Bottlenecks in your workflow

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check email and API token
2. **403 Forbidden**: Ensure your account has project access
3. **Rate Limiting**: Jira Cloud has API rate limits

### Required Permissions

Your Jira account needs:
- Browse Projects permission
- View Development Tools permission (for Git integration)
- Access to the projects you want to sync

### Logs

Check DevLake logs for detailed error information:
```bash
docker-compose logs devlake
```
