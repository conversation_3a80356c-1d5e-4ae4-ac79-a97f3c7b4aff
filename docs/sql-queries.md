# Custom SQL Queries for DevLake Dashboards

This document contains useful SQL queries for creating custom Grafana panels to analyze Coding Time and other metrics.

## Coding Time Metrics

### 1. Average Coding Time by Repository (Last 30 Days)

```sql
SELECT 
  repos.name as repository,
  ROUND(AVG(ppm.pr_coding_time / 60), 2) AS avg_coding_time_hours,
  COUNT(pr.id) as total_prs
FROM project_pr_metrics ppm
JOIN pull_requests pr ON pr.id = ppm.id
JOIN repos ON repos.id = pr.base_repo_id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
  AND ppm.pr_coding_time IS NOT NULL
  AND ppm.pr_coding_time > 0
GROUP BY repos.name
ORDER BY avg_coding_time_hours DESC;
```

### 2. Coding Time Trend Over Time

```sql
SELECT 
  DATE(pr.created_date) as time,
  ROUND(AVG(ppm.pr_coding_time / 60), 2) AS avg_coding_time_hours,
  COUNT(pr.id) as pr_count
FROM project_pr_metrics ppm
JOIN pull_requests pr ON pr.id = ppm.id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 90 DAY)
  AND ppm.pr_coding_time IS NOT NULL
  AND ppm.pr_coding_time > 0
GROUP BY DATE(pr.created_date)
ORDER BY time;
```

### 3. Coding Time Distribution

```sql
SELECT 
  CASE 
    WHEN ppm.pr_coding_time / 3600 <= 1 THEN '0-1 hours'
    WHEN ppm.pr_coding_time / 3600 <= 4 THEN '1-4 hours'
    WHEN ppm.pr_coding_time / 3600 <= 8 THEN '4-8 hours'
    WHEN ppm.pr_coding_time / 3600 <= 24 THEN '8-24 hours'
    WHEN ppm.pr_coding_time / 3600 <= 72 THEN '1-3 days'
    ELSE '3+ days'
  END as coding_time_range,
  COUNT(*) as pr_count,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM project_pr_metrics ppm
JOIN pull_requests pr ON pr.id = ppm.id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
  AND ppm.pr_coding_time IS NOT NULL
  AND ppm.pr_coding_time > 0
GROUP BY 
  CASE 
    WHEN ppm.pr_coding_time / 3600 <= 1 THEN '0-1 hours'
    WHEN ppm.pr_coding_time / 3600 <= 4 THEN '1-4 hours'
    WHEN ppm.pr_coding_time / 3600 <= 8 THEN '4-8 hours'
    WHEN ppm.pr_coding_time / 3600 <= 24 THEN '8-24 hours'
    WHEN ppm.pr_coding_time / 3600 <= 72 THEN '1-3 days'
    ELSE '3+ days'
  END
ORDER BY 
  CASE coding_time_range
    WHEN '0-1 hours' THEN 1
    WHEN '1-4 hours' THEN 2
    WHEN '4-8 hours' THEN 3
    WHEN '8-24 hours' THEN 4
    WHEN '1-3 days' THEN 5
    ELSE 6
  END;
```

### 4. Top Contributors by Coding Time

```sql
SELECT 
  pr.author_name as developer,
  COUNT(pr.id) as total_prs,
  ROUND(AVG(ppm.pr_coding_time / 60), 2) AS avg_coding_time_hours,
  ROUND(SUM(ppm.pr_coding_time / 60), 2) AS total_coding_time_hours
FROM project_pr_metrics ppm
JOIN pull_requests pr ON pr.id = ppm.id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
  AND ppm.pr_coding_time IS NOT NULL
  AND ppm.pr_coding_time > 0
GROUP BY pr.author_name
HAVING COUNT(pr.id) >= 3  -- Only developers with 3+ PRs
ORDER BY avg_coding_time_hours ASC;
```

## Pull Request Metrics

### 5. PR Review Time vs Coding Time

```sql
SELECT 
  repos.name as repository,
  ROUND(AVG(ppm.pr_coding_time / 60), 2) AS avg_coding_time_hours,
  ROUND(AVG(ppm.pr_review_time / 60), 2) AS avg_review_time_hours,
  ROUND(AVG((ppm.pr_coding_time + ppm.pr_review_time) / 60), 2) AS avg_total_time_hours
FROM project_pr_metrics ppm
JOIN pull_requests pr ON pr.id = ppm.id
JOIN repos ON repos.id = pr.base_repo_id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
  AND ppm.pr_coding_time IS NOT NULL
  AND ppm.pr_review_time IS NOT NULL
GROUP BY repos.name
ORDER BY avg_total_time_hours DESC;
```

### 6. PR Size vs Coding Time Correlation

```sql
SELECT 
  CASE 
    WHEN pr.additions + pr.deletions <= 50 THEN 'Small (≤50 lines)'
    WHEN pr.additions + pr.deletions <= 200 THEN 'Medium (51-200 lines)'
    WHEN pr.additions + pr.deletions <= 500 THEN 'Large (201-500 lines)'
    ELSE 'Extra Large (500+ lines)'
  END as pr_size,
  COUNT(*) as pr_count,
  ROUND(AVG(ppm.pr_coding_time / 60), 2) AS avg_coding_time_hours,
  ROUND(AVG(pr.additions + pr.deletions), 0) as avg_lines_changed
FROM project_pr_metrics ppm
JOIN pull_requests pr ON pr.id = ppm.id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
  AND ppm.pr_coding_time IS NOT NULL
  AND ppm.pr_coding_time > 0
  AND (pr.additions IS NOT NULL OR pr.deletions IS NOT NULL)
GROUP BY 
  CASE 
    WHEN pr.additions + pr.deletions <= 50 THEN 'Small (≤50 lines)'
    WHEN pr.additions + pr.deletions <= 200 THEN 'Medium (51-200 lines)'
    WHEN pr.additions + pr.deletions <= 500 THEN 'Large (201-500 lines)'
    ELSE 'Extra Large (500+ lines)'
  END
ORDER BY avg_coding_time_hours;
```

## Jira Integration Metrics

### 7. Issue to PR Linking

```sql
SELECT 
  i.issue_key,
  i.summary as issue_title,
  i.type as issue_type,
  pr.title as pr_title,
  ROUND(ppm.pr_coding_time / 60, 2) AS coding_time_hours,
  pr.created_date as pr_created,
  i.created_date as issue_created
FROM issues i
JOIN pull_requests pr ON pr.title LIKE CONCAT('%', i.issue_key, '%')
  OR pr.head_ref LIKE CONCAT('%', i.issue_key, '%')
JOIN project_pr_metrics ppm ON ppm.id = pr.id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
  AND ppm.pr_coding_time IS NOT NULL
ORDER BY coding_time_hours DESC;
```

### 8. Story Points vs Coding Time

```sql
SELECT 
  COALESCE(i.story_point, 0) as story_points,
  COUNT(pr.id) as pr_count,
  ROUND(AVG(ppm.pr_coding_time / 60), 2) AS avg_coding_time_hours
FROM issues i
JOIN pull_requests pr ON pr.title LIKE CONCAT('%', i.issue_key, '%')
JOIN project_pr_metrics ppm ON ppm.id = pr.id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
  AND ppm.pr_coding_time IS NOT NULL
  AND i.story_point IS NOT NULL
GROUP BY i.story_point
ORDER BY story_points;
```

## Team Performance Metrics

### 9. Team Coding Time Comparison

```sql
SELECT 
  CASE 
    WHEN pr.author_name IN ('dev1', 'dev2', 'dev3') THEN 'Team A'
    WHEN pr.author_name IN ('dev4', 'dev5', 'dev6') THEN 'Team B'
    ELSE 'Other'
  END as team,
  COUNT(pr.id) as total_prs,
  ROUND(AVG(ppm.pr_coding_time / 60), 2) AS avg_coding_time_hours,
  ROUND(STDDEV(ppm.pr_coding_time / 60), 2) AS coding_time_stddev
FROM project_pr_metrics ppm
JOIN pull_requests pr ON pr.id = ppm.id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
  AND ppm.pr_coding_time IS NOT NULL
GROUP BY 
  CASE 
    WHEN pr.author_name IN ('dev1', 'dev2', 'dev3') THEN 'Team A'
    WHEN pr.author_name IN ('dev4', 'dev5', 'dev6') THEN 'Team B'
    ELSE 'Other'
  END
ORDER BY avg_coding_time_hours;
```

### 10. Weekly Coding Time Trends

```sql
SELECT 
  YEAR(pr.created_date) as year,
  WEEK(pr.created_date) as week,
  COUNT(pr.id) as pr_count,
  ROUND(AVG(ppm.pr_coding_time / 60), 2) AS avg_coding_time_hours,
  ROUND(SUM(ppm.pr_coding_time / 60), 2) AS total_coding_time_hours
FROM project_pr_metrics ppm
JOIN pull_requests pr ON pr.id = ppm.id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 12 WEEK)
  AND ppm.pr_coding_time IS NOT NULL
GROUP BY YEAR(pr.created_date), WEEK(pr.created_date)
ORDER BY year, week;
```

## Usage Tips

1. **Time Zones**: All timestamps are in UTC. Adjust for your timezone if needed.
2. **Filtering**: Add `WHERE` clauses to filter by specific repositories, authors, or date ranges.
3. **Performance**: For large datasets, consider adding appropriate indexes.
4. **Null Values**: Always check for NULL values in time calculations.
5. **Grafana Variables**: Use Grafana variables like `$__timeFilter()` for dynamic time ranges.

## Creating Grafana Panels

1. In Grafana, create a new panel
2. Select "MySQL" as the data source
3. Paste one of the above queries
4. Choose appropriate visualization (Table, Time Series, Stat, etc.)
5. Configure panel options and styling as needed
