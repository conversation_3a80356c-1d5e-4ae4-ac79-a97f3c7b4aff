# Troubleshooting Guide

This guide helps resolve common issues when setting up and running DevLake.

## Common Setup Issues

### 1. Docker Compose Fails to Start

**Symptoms:**
- Services fail to start
- Port conflicts
- Permission errors

**Solutions:**

```bash
# Check if ports are already in use
netstat -tulpn | grep -E ':(3002|3306|4000|8080)'

# Stop conflicting services
sudo systemctl stop mysql  # If MySQL is running locally
sudo systemctl stop grafana-server  # If Grafana is running locally

# Check Docker daemon
sudo systemctl status docker
sudo systemctl start docker

# Reset Docker Compose
docker-compose down -v
docker-compose up -d
```

### 2. MySQL Connection Issues

**Symptoms:**
- DevLake API fails to connect to database
- "Access denied" errors
- Connection timeouts

**Solutions:**

```bash
# Check MySQL container logs
docker-compose logs mysql

# Test MySQL connection manually
docker-compose exec mysql mysql -u merico -p lake

# Reset MySQL data (WARNING: This deletes all data)
docker-compose down -v
docker volume rm devlake_mysql_data
docker-compose up -d
```

**Common MySQL Errors:**

```sql
-- Error: Access denied for user 'merico'@'%'
-- Solution: Check MYSQL_PASSWORD in .env.local

-- Error: Unknown database 'lake'
-- Solution: Check mysql/init.sql is properly mounted

-- Error: Character set issues
-- Solution: Ensure utf8mb4 is configured in init.sql
```

### 3. DevLake API Not Responding

**Symptoms:**
- Config UI shows "Cannot connect to DevLake API"
- API health check fails
- 500 Internal Server Error

**Solutions:**

```bash
# Check DevLake logs
docker-compose logs devlake

# Verify database connection
docker-compose exec devlake curl http://localhost:8080/api/ping

# Check environment variables
docker-compose exec devlake env | grep -E '(DB_URL|ENCRYPTION_SECRET)'

# Restart DevLake service
docker-compose restart devlake
```

### 4. Grafana Dashboard Issues

**Symptoms:**
- Dashboards not loading
- "No data" in panels
- Datasource connection errors

**Solutions:**

```bash
# Check Grafana logs
docker-compose logs grafana

# Verify datasource configuration
# Go to Grafana → Configuration → Data Sources

# Test MySQL connection from Grafana
# Use query: SELECT 1

# Reset Grafana data
docker-compose down
docker volume rm devlake_grafana_data
docker-compose up -d
```

## Integration Issues

### GitLab Integration

**Issue: 403 Forbidden**
```
Error: API rate limit exceeded
```

**Solutions:**
- Check Personal Access Token permissions
- Reduce sync frequency in blueprints
- Contact GitLab support for rate limit increase

**Issue: No data syncing**
```
Error: No projects found
```

**Solutions:**
- Verify GitLab URL format: `https://gitlab.com/api/v4`
- Check project visibility (private vs public)
- Ensure PAT has access to selected projects

### Jira Integration

**Issue: Authentication Failed**
```
Error: 401 Unauthorized
```

**Solutions:**
- Verify email address is correct
- Regenerate API token
- Check Jira instance URL format: `https://company.atlassian.net/rest/`

**Issue: Missing Issues**
```
Error: No issues found in project
```

**Solutions:**
- Check project permissions
- Verify issue types are selected
- Check JQL filters in blueprints

## Performance Issues

### Slow Dashboard Loading

**Symptoms:**
- Grafana panels timeout
- Queries take too long
- High CPU usage

**Solutions:**

```sql
-- Add indexes for better performance
CREATE INDEX idx_pr_created_date ON pull_requests(created_date);
CREATE INDEX idx_pr_metrics_coding_time ON project_pr_metrics(pr_coding_time);
CREATE INDEX idx_repos_name ON repos(name);

-- Optimize queries with date filters
SELECT * FROM pull_requests 
WHERE created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Use LIMIT for large datasets
SELECT * FROM pull_requests 
ORDER BY created_date DESC 
LIMIT 1000;
```

### High Memory Usage

**Solutions:**

```yaml
# In docker-compose.yml, add memory limits
services:
  mysql:
    mem_limit: 512m
  devlake:
    mem_limit: 1g
  grafana:
    mem_limit: 256m
```

## Data Quality Issues

### Missing Coding Time Data

**Symptoms:**
- Coding time shows as NULL or 0
- Inconsistent metrics

**Debugging:**

```sql
-- Check for PRs without coding time
SELECT pr.id, pr.title, pr.created_date, ppm.pr_coding_time
FROM pull_requests pr
LEFT JOIN project_pr_metrics ppm ON pr.id = ppm.id
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
  AND (ppm.pr_coding_time IS NULL OR ppm.pr_coding_time = 0);

-- Check commit data
SELECT pr.id, pr.title, COUNT(c.id) as commit_count
FROM pull_requests pr
LEFT JOIN pull_request_commits prc ON pr.id = prc.pull_request_id
LEFT JOIN commits c ON prc.commit_sha = c.sha
WHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY pr.id, pr.title
HAVING commit_count = 0;
```

### Incorrect Time Calculations

**Common Issues:**
- Timezone mismatches
- Draft PR handling
- Commit timestamp issues

**Solutions:**

```sql
-- Check timezone consistency
SELECT 
  pr.title,
  pr.created_date,
  MIN(c.authored_date) as first_commit,
  TIMESTAMPDIFF(SECOND, MIN(c.authored_date), pr.created_date) as time_diff
FROM pull_requests pr
JOIN pull_request_commits prc ON pr.id = prc.pull_request_id
JOIN commits c ON prc.commit_sha = c.sha
GROUP BY pr.id, pr.title, pr.created_date
HAVING time_diff < 0;  -- Negative values indicate issues
```

## Logging and Monitoring

### Enable Debug Logging

```yaml
# In docker-compose.yml
services:
  devlake:
    environment:
      LOGGING_LEVEL: DEBUG
```

### Monitor Resource Usage

```bash
# Check container resource usage
docker stats

# Check disk usage
docker system df

# Check logs size
du -sh $(docker inspect --format='{{.LogPath}}' $(docker-compose ps -q))
```

### Log Rotation

```yaml
# In docker-compose.yml
services:
  devlake:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## Recovery Procedures

### Complete Reset

```bash
# Stop all services
docker-compose down -v

# Remove all volumes (WARNING: Deletes all data)
docker volume prune -f

# Remove all containers
docker container prune -f

# Restart from scratch
./scripts/setup.sh
```

### Backup and Restore

```bash
# Backup MySQL data
docker-compose exec mysql mysqldump -u root -p lake > backup.sql

# Backup Grafana dashboards
docker cp devlake-grafana:/var/lib/grafana/grafana.db grafana-backup.db

# Restore MySQL data
docker-compose exec -T mysql mysql -u root -p lake < backup.sql
```

## Getting Help

### Log Collection

```bash
# Collect all logs
mkdir -p troubleshooting-logs
docker-compose logs > troubleshooting-logs/all-services.log
docker-compose logs devlake > troubleshooting-logs/devlake.log
docker-compose logs mysql > troubleshooting-logs/mysql.log
docker-compose logs grafana > troubleshooting-logs/grafana.log

# System information
docker version > troubleshooting-logs/docker-version.txt
docker-compose version > troubleshooting-logs/compose-version.txt
uname -a > troubleshooting-logs/system-info.txt
```

### Support Channels

- [DevLake GitHub Issues](https://github.com/apache/incubator-devlake/issues)
- [DevLake Slack Community](https://join.slack.com/t/devlake-io/shared_invite/zt-17b6vuvps-x98pqseoUagM7EAmKC82xQ)
- [DevLake Documentation](https://devlake.apache.org/docs/)

### When Reporting Issues

Include:
1. DevLake version
2. Docker and Docker Compose versions
3. Operating system
4. Error logs
5. Steps to reproduce
6. Expected vs actual behavior
