{"dashboard": {"id": null, "title": "Coding Time Metrics", "tags": ["<PERSON><PERSON><PERSON>", "coding-time", "gitlab", "jira"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "Average Coding Time by Repository", "type": "stat", "targets": [{"expr": "SELECT \n  repos.name as repository,\n  ROUND(AVG(ppm.pr_coding_time / 60), 2) AS avg_coding_time_hours\nFROM project_pr_metrics ppm\nJOIN pull_requests pr ON pr.id = ppm.id\nJOIN repos ON repos.id = pr.base_repo_id\nWHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\nGROUP BY repos.name\nORDER BY avg_coding_time_hours DESC", "format": "table", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Coding Time Trend (Last 30 Days)", "type": "timeseries", "targets": [{"expr": "SELECT \n  DATE(pr.created_date) as time,\n  ROUND(AVG(ppm.pr_coding_time / 60), 2) AS avg_coding_time_hours\nFROM project_pr_metrics ppm\nJOIN pull_requests pr ON pr.id = ppm.id\nWHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\nGROUP BY DATE(pr.created_date)\nORDER BY time", "format": "time_series", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Top 10 Longest Coding Time PRs", "type": "table", "targets": [{"expr": "SELECT \n  repos.name as Repository,\n  pr.title as 'PR Title',\n  pr.author_name as Author,\n  ROUND(ppm.pr_coding_time / 60, 2) AS 'Coding Time (Hours)',\n  pr.created_date as 'Created Date'\nFROM project_pr_metrics ppm\nJOIN pull_requests pr ON pr.id = ppm.id\nJOIN repos ON repos.id = pr.base_repo_id\nWHERE pr.created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\nORDER BY ppm.pr_coding_time DESC\nLIMIT 10", "format": "table", "refId": "A"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}], "time": {"from": "now-30d", "to": "now"}, "refresh": "5m"}}