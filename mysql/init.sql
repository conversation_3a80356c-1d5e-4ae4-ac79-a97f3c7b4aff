-- MySQL initialization script for DevLake
-- This script ensures proper database setup with required character sets

-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS `lake` CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;

-- Grant privileges to the DevLake user
GRANT ALL PRIVILEGES ON `lake`.* TO 'merico'@'%';
FLUSH PRIVILEGES;

-- Set proper SQL modes for DevLake compatibility
SET GLOBAL sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- Optimize MySQL settings for DevLake
SET GLOBAL innodb_buffer_pool_size = 268435456; -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL wait_timeout = 28800;
SET GLOBAL interactive_timeout = 28800;
