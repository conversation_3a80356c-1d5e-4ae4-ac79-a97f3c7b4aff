#!/bin/bash

# AWS Deployment Script for DevLake
# This script helps prepare and deploy DevLake to AWS

set -e

# Configuration
AWS_REGION=${AWS_REGION:-us-east-1}
STACK_NAME=${STACK_NAME:-devlake}
ENVIRONMENT=${ENVIRONMENT:-production}

echo "🚀 Preparing DevLake for AWS deployment..."

# Check AWS CLI
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if AWS credentials are configured
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS credentials not configured. Please run 'aws configure'."
    exit 1
fi

echo "📋 Current AWS Identity:"
aws sts get-caller-identity

# Function to create secrets in AWS Secrets Manager
create_secrets() {
    echo "🔐 Creating secrets in AWS Secrets Manager..."
    
    # Generate encryption secret if not provided
    if [ -z "$ENCRYPTION_SECRET" ]; then
        ENCRYPTION_SECRET=$(openssl rand -hex 16)
        echo "Generated encryption secret: $ENCRYPTION_SECRET"
    fi
    
    # Create the secret
    aws secretsmanager create-secret \
        --region $AWS_REGION \
        --name "${STACK_NAME}-secrets" \
        --description "DevLake configuration secrets for $ENVIRONMENT" \
        --secret-string "{
            \"MYSQL_ROOT_PASSWORD\": \"${MYSQL_ROOT_PASSWORD:-$(openssl rand -base64 32)}\",
            \"MYSQL_PASSWORD\": \"${MYSQL_PASSWORD:-$(openssl rand -base64 32)}\",
            \"ENCRYPTION_SECRET\": \"$ENCRYPTION_SECRET\",
            \"GRAFANA_ADMIN_PASSWORD\": \"${GRAFANA_ADMIN_PASSWORD:-$(openssl rand -base64 16)}\",
            \"GITLAB_TOKEN\": \"${GITLAB_TOKEN:-REPLACE_WITH_YOUR_TOKEN}\",
            \"JIRA_API_TOKEN\": \"${JIRA_API_TOKEN:-REPLACE_WITH_YOUR_TOKEN}\",
            \"JIRA_EMAIL\": \"${JIRA_EMAIL:-REPLACE_WITH_YOUR_EMAIL}\"
        }" \
        --tags Key=Environment,Value=$ENVIRONMENT Key=Application,Value=DevLake \
        || echo "Secret may already exist, updating..."
    
    echo "✅ Secrets created/updated in AWS Secrets Manager"
}

# Function to create RDS instance
create_rds() {
    echo "🗄️ Creating RDS MySQL instance..."
    
    # Create DB subnet group if it doesn't exist
    aws rds create-db-subnet-group \
        --region $AWS_REGION \
        --db-subnet-group-name "${STACK_NAME}-db-subnet-group" \
        --db-subnet-group-description "DevLake DB subnet group" \
        --subnet-ids subnet-xxxxxxxx subnet-yyyyyyyy \
        --tags Key=Environment,Value=$ENVIRONMENT \
        || echo "DB subnet group may already exist"
    
    # Create RDS instance
    aws rds create-db-instance \
        --region $AWS_REGION \
        --db-instance-identifier "${STACK_NAME}-mysql" \
        --db-instance-class db.t3.micro \
        --engine mysql \
        --engine-version 8.0.35 \
        --master-username admin \
        --master-user-password "${MYSQL_ROOT_PASSWORD:-TempPassword123}" \
        --allocated-storage 20 \
        --storage-type gp3 \
        --db-subnet-group-name "${STACK_NAME}-db-subnet-group" \
        --vpc-security-group-ids sg-xxxxxxxxx \
        --backup-retention-period 7 \
        --storage-encrypted \
        --tags Key=Environment,Value=$ENVIRONMENT Key=Application,Value=DevLake \
        || echo "RDS instance may already exist"
    
    echo "✅ RDS instance creation initiated"
}

# Function to build and push Docker images to ECR
build_and_push_images() {
    echo "🐳 Building and pushing Docker images to ECR..."
    
    # Get AWS account ID
    ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    ECR_REGISTRY="${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"
    
    # Login to ECR
    aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
    
    # Create ECR repositories if they don't exist
    aws ecr create-repository --region $AWS_REGION --repository-name devlake/api || true
    aws ecr create-repository --region $AWS_REGION --repository-name devlake/config-ui || true
    aws ecr create-repository --region $AWS_REGION --repository-name devlake/grafana || true
    
    echo "✅ ECR repositories ready"
}

# Function to deploy using CloudFormation or CDK
deploy_infrastructure() {
    echo "☁️ Deploying infrastructure..."
    
    # This would typically use CloudFormation templates or CDK
    # For now, we'll create a basic ECS service
    
    echo "📝 Creating ECS cluster..."
    aws ecs create-cluster \
        --region $AWS_REGION \
        --cluster-name "${STACK_NAME}-cluster" \
        --tags key=Environment,value=$ENVIRONMENT \
        || echo "Cluster may already exist"
    
    echo "✅ Infrastructure deployment initiated"
}

# Main deployment flow
main() {
    echo "🎯 Deploying DevLake to AWS"
    echo "   Region: $AWS_REGION"
    echo "   Stack: $STACK_NAME"
    echo "   Environment: $ENVIRONMENT"
    echo ""
    
    # Load environment variables from .env.local if it exists
    if [ -f .env.local ]; then
        export $(grep -v '^#' .env.local | xargs)
    fi
    
    # Create secrets
    create_secrets
    
    # Create RDS (optional - comment out if using existing DB)
    # create_rds
    
    # Build and push images (optional - comment out if using public images)
    # build_and_push_images
    
    # Deploy infrastructure
    deploy_infrastructure
    
    echo ""
    echo "🎉 AWS deployment preparation complete!"
    echo ""
    echo "📋 Next steps:"
    echo "   1. Update security group IDs in the deployment scripts"
    echo "   2. Update subnet IDs for your VPC"
    echo "   3. Configure your domain and SSL certificates"
    echo "   4. Update the secrets in AWS Secrets Manager with real values"
    echo "   5. Deploy the ECS services or EC2 instances"
    echo ""
    echo "🔗 Useful commands:"
    echo "   aws secretsmanager get-secret-value --secret-id ${STACK_NAME}-secrets"
    echo "   aws rds describe-db-instances --db-instance-identifier ${STACK_NAME}-mysql"
    echo "   aws ecs list-clusters"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --region)
            AWS_REGION="$2"
            shift 2
            ;;
        --stack-name)
            STACK_NAME="$2"
            shift 2
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [--region REGION] [--stack-name NAME] [--environment ENV]"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

# Run main function
main
