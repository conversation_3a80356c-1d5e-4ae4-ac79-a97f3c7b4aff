#!/bin/bash

# DevLake Setup Script
# This script initializes the DevLake environment

set -e

echo "🚀 Setting up Apache DevLake..."

# Check if <PERSON><PERSON> and <PERSON>er Compose are installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env.local from .env template if it doesn't exist
if [ ! -f .env.local ]; then
    echo "📝 Creating .env.local from template..."
    cp .env .env.local

    # Generate a random encryption secret
    ENCRYPTION_SECRET=$(openssl rand -hex 16)
    sed -i.bak "s/your-32-character-encryption-secret/$ENCRYPTION_SECRET/" .env.local
    rm .env.local.bak

    echo "⚠️  Please edit .env.local and update the following:"
    echo "   - GitLab Personal Access Token"
    echo "   - Jira API credentials"
    echo "   - Any other configuration as needed"
    echo ""
fi

# Use .env.local for environment variables
export $(grep -v '^#' .env.local | xargs)

echo "🐳 Starting DevLake services..."
docker-compose --env-file .env.local up -d

echo "⏳ Waiting for services to be ready..."
sleep 30

# Check service health
echo "🔍 Checking service health..."

# Check DevLake API basic health
if curl -f http://localhost:8080/ping > /dev/null 2>&1; then
    echo "✅ DevLake API is responding"

    # Check if database migration is needed
    echo "🔄 Checking database migration status..."
    if curl -f http://localhost:8080/plugins > /dev/null 2>&1; then
        echo "✅ DevLake API fully initialized"
    else
        echo "🔧 Triggering database migration..."
        if curl -f http://localhost:8080/proceed-db-migration > /dev/null 2>&1; then
            echo "✅ Database migration completed"
            echo "🔄 Restarting DevLake API..."
            docker-compose restart devlake > /dev/null 2>&1
            sleep 15
            echo "🔄 Restarting Config UI..."
            docker-compose restart config-ui > /dev/null 2>&1
            sleep 10
        else
            echo "⚠️  Database migration failed"
        fi
    fi
else
    echo "❌ DevLake API is not responding"
fi

# Check Config UI
if curl -f http://localhost:4000 > /dev/null 2>&1; then
    echo "✅ Config UI is running"
else
    echo "❌ Config UI is not responding"
fi

# Check Grafana
if curl -f http://localhost:3002 > /dev/null 2>&1; then
    echo "✅ Grafana is running"
else
    echo "❌ Grafana is not responding"
fi

echo ""
echo "🎉 DevLake setup complete!"
echo ""
echo "📊 Access your services:"
echo "   Config UI: http://localhost:4000"
echo "   Grafana:   http://localhost:3002 (admin/admin123)"
echo "   API:       http://localhost:8080"
echo ""
echo "📖 Next steps:"
echo "   1. Open Config UI and add your GitLab connection"
echo "   2. Add your Jira Cloud connection"
echo "   3. Configure data sync blueprints"
echo "   4. View dashboards in Grafana"
echo ""
