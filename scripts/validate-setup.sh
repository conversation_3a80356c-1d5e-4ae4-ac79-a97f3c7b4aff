#!/bin/bash

# DevLake Setup Validation Script
# This script validates that the DevLake environment is properly configured

set -e

echo "🔍 Validating DevLake setup..."

# Check if required files exist
check_files() {
    echo "📁 Checking required files..."

    required_files=(
        "docker-compose.yml"
        ".env"
        "mysql/init.sql"
        "grafana/provisioning/datasources/devlake.yml"
        "config/config.yaml"
    )

    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            echo "✅ $file exists"
        else
            echo "❌ $file is missing"
            exit 1
        fi
    done
}

# Check if .env.local exists and has required variables
check_env() {
    echo "🔧 Checking environment configuration..."

    if [ ! -f ".env.local" ]; then
        echo "⚠️  .env.local not found. Creating from template..."
        cp .env .env.local

        # Generate encryption secret
        ENCRYPTION_SECRET=$(openssl rand -hex 16)
        sed -i.bak "s/your-32-character-encryption-secret/$ENCRYPTION_SECRET/" .env.local
        rm .env.local.bak

        echo "✅ .env.local created with generated encryption secret"
        echo "⚠️  Please update Git<PERSON>ab and Jira credentials in .env.local"
    else
        echo "✅ .env.local exists"
    fi

    # Check for required variables
    source .env.local

    if [ -z "$ENCRYPTION_SECRET" ] || [ "$ENCRYPTION_SECRET" = "your-32-character-encryption-secret" ]; then
        echo "⚠️  ENCRYPTION_SECRET needs to be updated in .env.local"
    else
        echo "✅ ENCRYPTION_SECRET is configured"
    fi
}

# Check Docker and Docker Compose
check_docker() {
    echo "🐳 Checking Docker setup..."

    if ! command -v docker &> /dev/null; then
        echo "❌ Docker is not installed"
        exit 1
    else
        echo "✅ Docker is installed"
    fi

    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose is not installed"
        exit 1
    else
        echo "✅ Docker Compose is installed"
    fi

    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        echo "❌ Docker daemon is not running"
        exit 1
    else
        echo "✅ Docker daemon is running"
    fi
}

# Validate Docker Compose configuration
validate_compose() {
    echo "📋 Validating Docker Compose configuration..."

    if docker-compose --env-file .env.local config &> /dev/null; then
        echo "✅ Docker Compose configuration is valid"
    else
        echo "❌ Docker Compose configuration has errors"
        docker-compose --env-file .env.local config
        exit 1
    fi
}

# Check if services are running (if Docker Compose is up)
check_services() {
    echo "🔍 Checking service status..."

    if docker-compose ps | grep -q "Up"; then
        echo "📊 Services are running:"
        docker-compose ps

        # Test service endpoints
        echo "🌐 Testing service endpoints..."

        # Test DevLake API
        if curl -f -s http://localhost:8080/ping > /dev/null; then
            echo "✅ DevLake API is responding"
        else
            echo "⚠️  DevLake API is not responding (may still be starting)"
        fi

        # Test Config UI
        if curl -f -s http://localhost:4000 > /dev/null; then
            echo "✅ Config UI is responding"
        else
            echo "⚠️  Config UI is not responding (may still be starting)"
        fi

        # Test Grafana
        if curl -f -s http://localhost:3002 > /dev/null; then
            echo "✅ Grafana is responding"
        else
            echo "⚠️  Grafana is not responding (may still be starting)"
        fi
    else
        echo "ℹ️  Services are not running. Use 'docker-compose up -d' to start them."
    fi
}

# Main validation function
main() {
    echo "🎯 DevLake Setup Validation"
    echo "=========================="
    echo ""

    check_files
    echo ""

    check_env
    echo ""

    check_docker
    echo ""

    validate_compose
    echo ""

    check_services
    echo ""

    echo "🎉 Validation complete!"
    echo ""
    echo "📋 Next steps:"
    echo "   1. If services aren't running: ./scripts/setup.sh"
    echo "   2. Configure GitLab connection: http://localhost:4000"
    echo "   3. Configure Jira connection: http://localhost:4000"
    echo "   4. View dashboards: http://localhost:3002"
    echo ""
}

# Run validation
main
